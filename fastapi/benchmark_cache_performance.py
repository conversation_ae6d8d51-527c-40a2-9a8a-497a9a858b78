#!/usr/bin/env python3
"""
Performance Benchmark Script for FastAPI Redis Caching Implementation

This script measures the performance improvement achieved by implementing Redis caching
for database-accessing endpoints in the FastAPI application.

It tests:
1. Cache miss scenarios (first request - database query)
2. Cache hit scenarios (subsequent requests - Redis cache)
3. Performance comparison and improvement calculations
"""

import requests
import time
import statistics
import json
from typing import List, Dict, Tuple
import redis
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
BASE_URL = "http://localhost:8001"
API_TOKEN = "Xq7Zy9Bk2Lm3Hn5Jf8Uw1Pv6Rt0Oi4Ns9Qe7Cd8Mx2Gy6Tj5"
HEADERS = {"Authorization": f"Bearer {API_TOKEN}"}

# Test parameters
TEST_USER_ID = 1
TEST_FROM_ID = 1
TEST_CHAT_ID = 2
NUM_ITERATIONS = 5  # Number of times to run each test

class PerformanceBenchmark:
    def __init__(self):
        self.redis_client = self._connect_redis()
        self.results = {}
    
    def _connect_redis(self):
        """Connect to Redis for cache management."""
        try:
            client = redis.Redis(
                host=os.getenv('REDIS_HOST', 'localhost'),
                port=int(os.getenv('REDIS_PORT', 6379)),
                password=os.getenv('REDIS_PASSWORD') if os.getenv('REDIS_PASSWORD') else None,
                db=int(os.getenv('REDIS_DB', 0)),
                decode_responses=True
            )
            client.ping()
            print("✅ Connected to Redis successfully")
            return client
        except Exception as e:
            print(f"❌ Failed to connect to Redis: {e}")
            return None
    
    def clear_cache(self, pattern: str = "*"):
        """Clear Redis cache for fresh testing."""
        if self.redis_client:
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)
                print(f"🧹 Cleared {len(keys)} cache keys")
    
    def measure_response_time(self, url: str, params: Dict) -> Tuple[float, bool, Dict]:
        """Measure response time for a single request."""
        start_time = time.time()
        try:
            response = requests.get(url, headers=HEADERS, params=params, timeout=10)
            end_time = time.time()
            
            response_time = (end_time - start_time) * 1000  # Convert to milliseconds
            success = response.status_code == 200
            data = response.json() if success else {}
            
            return response_time, success, data
        except Exception as e:
            end_time = time.time()
            response_time = (end_time - start_time) * 1000
            print(f"❌ Request failed: {e}")
            return response_time, False, {}
    
    def benchmark_endpoint(self, endpoint_name: str, url: str, params: Dict) -> Dict:
        """Benchmark a specific endpoint for cache miss vs cache hit performance."""
        print(f"\n🔍 Benchmarking {endpoint_name}")
        print("=" * 50)
        
        # Test 1: Cache Miss (clear cache first)
        print("📊 Testing Cache MISS scenarios...")
        cache_miss_times = []
        
        for i in range(NUM_ITERATIONS):
            # Clear cache before each miss test
            self.clear_cache()
            time.sleep(0.1)  # Small delay to ensure cache is cleared
            
            response_time, success, data = self.measure_response_time(url, params)
            if success:
                cache_miss_times.append(response_time)
                print(f"   Iteration {i+1}: {response_time:.2f}ms")
            else:
                print(f"   Iteration {i+1}: FAILED")
        
        # Test 2: Cache Hit (don't clear cache)
        print("\n📊 Testing Cache HIT scenarios...")
        cache_hit_times = []
        
        # First request to populate cache
        self.clear_cache()
        self.measure_response_time(url, params)
        time.sleep(0.1)
        
        for i in range(NUM_ITERATIONS):
            response_time, success, data = self.measure_response_time(url, params)
            if success:
                cache_hit_times.append(response_time)
                print(f"   Iteration {i+1}: {response_time:.2f}ms")
            else:
                print(f"   Iteration {i+1}: FAILED")
        
        # Calculate statistics
        if cache_miss_times and cache_hit_times:
            miss_avg = statistics.mean(cache_miss_times)
            hit_avg = statistics.mean(cache_hit_times)
            improvement = ((miss_avg - hit_avg) / miss_avg) * 100
            speedup = miss_avg / hit_avg
            
            results = {
                'endpoint': endpoint_name,
                'cache_miss': {
                    'times': cache_miss_times,
                    'avg': miss_avg,
                    'min': min(cache_miss_times),
                    'max': max(cache_miss_times),
                    'std': statistics.stdev(cache_miss_times) if len(cache_miss_times) > 1 else 0
                },
                'cache_hit': {
                    'times': cache_hit_times,
                    'avg': hit_avg,
                    'min': min(cache_hit_times),
                    'max': max(cache_hit_times),
                    'std': statistics.stdev(cache_hit_times) if len(cache_hit_times) > 1 else 0
                },
                'improvement_percent': improvement,
                'speedup_factor': speedup
            }
            
            print(f"\n📈 Results for {endpoint_name}:")
            print(f"   Cache MISS avg: {miss_avg:.2f}ms")
            print(f"   Cache HIT avg:  {hit_avg:.2f}ms")
            print(f"   Improvement:    {improvement:.1f}%")
            print(f"   Speedup:        {speedup:.1f}x faster")
            
            return results
        else:
            print(f"❌ Failed to collect sufficient data for {endpoint_name}")
            return {}
    
    def run_all_benchmarks(self):
        """Run benchmarks for all endpoints."""
        print("🚀 Starting FastAPI Redis Cache Performance Benchmark")
        print("=" * 60)
        
        # Test endpoints
        endpoints = [
            {
                'name': '/user_summary',
                'url': f"{BASE_URL}/user_summary",
                'params': {'user_id': TEST_USER_ID}
            },
            {
                'name': '/user_kb',
                'url': f"{BASE_URL}/user_kb",
                'params': {'user_id': TEST_USER_ID}
            },
            {
                'name': '/chat_history',
                'url': f"{BASE_URL}/chat_history",
                'params': {'from_id': TEST_FROM_ID, 'chat_id': TEST_CHAT_ID}
            },
            {
                'name': '/conversation',
                'url': f"{BASE_URL}/conversation",
                'params': {'from_id': TEST_FROM_ID, 'chat_id': TEST_CHAT_ID}
            }
        ]
        
        all_results = []
        
        for endpoint in endpoints:
            result = self.benchmark_endpoint(
                endpoint['name'],
                endpoint['url'],
                endpoint['params']
            )
            if result:
                all_results.append(result)
                self.results[endpoint['name']] = result
        
        # Generate summary report
        self.generate_summary_report(all_results)
        
        return all_results
    
    def generate_summary_report(self, results: List[Dict]):
        """Generate a comprehensive summary report."""
        print("\n" + "=" * 60)
        print("📊 PERFORMANCE BENCHMARK SUMMARY REPORT")
        print("=" * 60)
        
        if not results:
            print("❌ No results to display")
            return
        
        print(f"{'Endpoint':<15} {'Cache Miss':<12} {'Cache Hit':<12} {'Improvement':<12} {'Speedup':<10}")
        print("-" * 65)
        
        total_improvement = 0
        total_speedup = 0
        
        for result in results:
            endpoint = result['endpoint']
            miss_avg = result['cache_miss']['avg']
            hit_avg = result['cache_hit']['avg']
            improvement = result['improvement_percent']
            speedup = result['speedup_factor']
            
            print(f"{endpoint:<15} {miss_avg:>8.1f}ms {hit_avg:>8.1f}ms {improvement:>8.1f}% {speedup:>8.1f}x")
            
            total_improvement += improvement
            total_speedup += speedup
        
        avg_improvement = total_improvement / len(results)
        avg_speedup = total_speedup / len(results)
        
        print("-" * 65)
        print(f"{'AVERAGE':<15} {'':<12} {'':<12} {avg_improvement:>8.1f}% {avg_speedup:>8.1f}x")
        
        print(f"\n🎯 Key Findings:")
        print(f"   • Average performance improvement: {avg_improvement:.1f}%")
        print(f"   • Average speedup factor: {avg_speedup:.1f}x")
        print(f"   • Redis caching significantly reduces database load")
        print(f"   • Response times are more consistent with caching")
        
        # Save results to file
        with open('benchmark_results.json', 'w') as f:
            json.dump(self.results, f, indent=2)
        print(f"\n💾 Detailed results saved to: benchmark_results.json")

def main():
    """Main function to run the benchmark."""
    benchmark = PerformanceBenchmark()
    
    # Check if FastAPI server is running
    try:
        response = requests.get(f"{BASE_URL}/user_summary", 
                              headers=HEADERS, 
                              params={'user_id': 1}, 
                              timeout=5)
        if response.status_code != 200:
            print("❌ FastAPI server is not responding correctly")
            print("   Please ensure the server is running on http://localhost:8000")
            return
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to FastAPI server")
        print("   Please start the server with: python fastapi/app.py")
        return
    
    print("✅ FastAPI server is running")
    
    # Run benchmarks
    results = benchmark.run_all_benchmarks()
    
    if results:
        print("\n🎉 Benchmark completed successfully!")
        print("   Check the summary above for performance improvements.")
    else:
        print("\n❌ Benchmark failed to complete")

if __name__ == "__main__":
    main()
