#!/usr/bin/env python3
"""
Simple test script to verify Redis caching functionality
"""

import requests
import time
import redis
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
BASE_URL = "http://localhost:8001"
API_TOKEN = "Xq7Zy9Bk2Lm3Hn5Jf8Uw1Pv6Rt0Oi4Ns9Qe7Cd8Mx2Gy6Tj5"
HEADERS = {"Authorization": f"Bearer {API_TOKEN}"}

def connect_redis():
    """Connect to Redis."""
    try:
        client = redis.Redis(
            host=os.getenv('REDIS_HOST', 'localhost'),
            port=int(os.getenv('REDIS_PORT', 6379)),
            password=os.getenv('REDIS_PASSWORD') if os.getenv('REDIS_PASSWORD') else None,
            db=int(os.getenv('REDIS_DB', 0)),
            decode_responses=True
        )
        client.ping()
        return client
    except Exception as e:
        print(f"❌ Failed to connect to Redis: {e}")
        return None

def test_endpoint_caching(endpoint_name, url, params):
    """Test caching for a specific endpoint."""
    print(f"\n🧪 Testing {endpoint_name}")
    print("-" * 40)
    
    redis_client = connect_redis()
    if not redis_client:
        return False
    
    # Clear any existing cache
    redis_client.flushdb()
    print("🧹 Cleared Redis cache")
    
    # First request (should be cache miss)
    print("📡 Making first request (cache miss)...")
    start_time = time.time()
    response1 = requests.get(url, headers=HEADERS, params=params)
    end_time = time.time()
    time1 = (end_time - start_time) * 1000
    
    if response1.status_code != 200:
        print(f"❌ First request failed: {response1.status_code}")
        return False
    
    print(f"✅ First request completed in {time1:.2f}ms")
    
    # Check if data was cached
    cache_keys = redis_client.keys("*")
    print(f"🔍 Cache keys after first request: {len(cache_keys)}")
    
    # Second request (should be cache hit)
    print("📡 Making second request (cache hit)...")
    start_time = time.time()
    response2 = requests.get(url, headers=HEADERS, params=params)
    end_time = time.time()
    time2 = (end_time - start_time) * 1000
    
    if response2.status_code != 200:
        print(f"❌ Second request failed: {response2.status_code}")
        return False
    
    print(f"✅ Second request completed in {time2:.2f}ms")
    
    # Verify responses are identical
    if response1.json() == response2.json():
        print("✅ Response data is identical")
    else:
        print("❌ Response data differs between requests")
        return False
    
    # Calculate improvement
    if time2 < time1:
        improvement = ((time1 - time2) / time1) * 100
        speedup = time1 / time2
        print(f"🚀 Performance improvement: {improvement:.1f}% ({speedup:.1f}x faster)")
    else:
        print("⚠️  Second request was not faster (possible cache miss)")
    
    return True

def main():
    """Main test function."""
    print("🧪 Redis Cache Functionality Test")
    print("=" * 50)
    
    # Test endpoints
    endpoints = [
        {
            'name': '/user_summary',
            'url': f"{BASE_URL}/user_summary",
            'params': {'user_id': 54}
        },
        {
            'name': '/user_kb',
            'url': f"{BASE_URL}/user_kb",
            'params': {'user_id': 54}
        },
        {
            'name': '/chat_history',
            'url': f"{BASE_URL}/chat_history",
            'params': {'from_id': 54, 'chat_id': 4}
        },
        {
            'name': '/conversation',
            'url': f"{BASE_URL}/conversation",
            'params': {'from_id': 54, 'chat_id': 4}
        }
    ]
    
    success_count = 0
    total_count = len(endpoints)
    
    for endpoint in endpoints:
        success = test_endpoint_caching(
            endpoint['name'],
            endpoint['url'],
            endpoint['params']
        )
        if success:
            success_count += 1
    
    print(f"\n📊 Test Results: {success_count}/{total_count} endpoints passed")
    
    if success_count == total_count:
        print("🎉 All caching tests passed successfully!")
    else:
        print("❌ Some caching tests failed")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
