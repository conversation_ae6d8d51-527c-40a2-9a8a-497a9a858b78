#!/usr/bin/env python3
"""
Redis缓存内存监控脚本
"""

import redis
import time
import os
from dotenv import load_dotenv

load_dotenv()

def connect_redis():
    """连接Redis"""
    try:
        client = redis.Redis(
            host=os.getenv('REDIS_HOST', 'localhost'),
            port=int(os.getenv('REDIS_PORT', 6379)),
            password=os.getenv('REDIS_PASSWORD') if os.getenv('REDIS_PASSWORD') else None,
            db=int(os.getenv('REDIS_DB', 0)),
            decode_responses=True
        )
        client.ping()
        return client
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return None

def get_memory_info(redis_client):
    """获取内存使用信息"""
    try:
        info = redis_client.info()
        keys_count = redis_client.dbsize()
        
        # 获取各类型缓存的数量
        cache_types = {
            'user_summary': len(redis_client.keys('user_summary:*')),
            'user_kb': len(redis_client.keys('user_kb:*')),
            'chat_history': len(redis_client.keys('chat_history:*')),
            'conversation': len(redis_client.keys('conversation:*'))
        }
        
        return {
            'total_keys': keys_count,
            'used_memory_bytes': info.get('used_memory', 0),
            'used_memory_human': info.get('used_memory_human', 'N/A'),
            'used_memory_peak_human': info.get('used_memory_peak_human', 'N/A'),
            'keyspace_hits': info.get('keyspace_hits', 0),
            'keyspace_misses': info.get('keyspace_misses', 0),
            'cache_types': cache_types
        }
    except Exception as e:
        print(f"❌ 获取内存信息失败: {e}")
        return None

def monitor_memory(interval=60):
    """持续监控内存使用"""
    redis_client = connect_redis()
    if not redis_client:
        return
    
    print("🔍 Redis缓存内存监控启动")
    print("=" * 60)
    
    try:
        while True:
            info = get_memory_info(redis_client)
            if info:
                hits = info['keyspace_hits']
                misses = info['keyspace_misses']
                hit_rate = (hits / (hits + misses)) * 100 if (hits + misses) > 0 else 0
                
                print(f"\n⏰ {time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"📊 总缓存键数: {info['total_keys']}")
                print(f"💾 内存使用: {info['used_memory_human']}")
                print(f"📈 峰值内存: {info['used_memory_peak_human']}")
                print(f"🎯 缓存命中率: {hit_rate:.2f}%")
                print(f"📋 缓存类型分布:")
                for cache_type, count in info['cache_types'].items():
                    print(f"   - {cache_type}: {count} 个")
                
                # 内存警告
                memory_mb = info['used_memory_bytes'] / (1024 * 1024)
                if memory_mb > 100:  # 超过100MB警告
                    print(f"⚠️  内存使用较高: {memory_mb:.2f}MB")
                
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n👋 监控已停止")

if __name__ == "__main__":
    import sys
    interval = int(sys.argv[1]) if len(sys.argv) > 1 else 60
    monitor_memory(interval)
