from fastapi import Depends, FastAPI, HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
import mysql.connector
from mysql.connector import pooling
import logging
from logging.handlers import RotatingFileHandler
import os
from dotenv import load_dotenv
from contextlib import contextmanager

# Load environment variables
load_dotenv()

app = FastAPI()
security = HTTPBearer()

# Ensure logs directory exists
os.makedirs('/home/<USER>/fastapi/logs', exist_ok=True)

# Configure logging with both console and file handlers
logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s [%(levelname)s] %(message)s",
    handlers=[
        logging.StreamHandler(),  # Console output
        RotatingFileHandler(
            '/home/<USER>/fastapi/logs/app.log',
            maxBytes=10*1024*1024,  # 10 MB
            backupCount=5  # Keep 5 backup files
        )
    ]
)

# Create a connection pool
def create_db_pool():
    """Create a connection pool for MySQL database."""
    try:
        return mysql.connector.pooling.MySQLConnectionPool(
            pool_name="mypool",
            pool_size=5,  # Adjust based on your expected concurrent connections
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            database=os.getenv('DB_NAME')
        )
    except mysql.connector.Error as err:
        logging.error(f"Error creating connection pool: {err}")
        raise HTTPException(status_code=500, detail="Database connection pool error")

# Create the connection pool when the app starts
db_pool = create_db_pool()

@contextmanager
def get_db_connection():
    """
    Context manager to get a database connection from the pool.
    Automatically returns the connection to the pool when done.
    """
    connection = None
    try:
        connection = db_pool.get_connection()
        yield connection
    except mysql.connector.Error as err:
        logging.error(f"Database connection error: {err}")
        raise HTTPException(status_code=500, detail="Database connection error")
    finally:
        if connection:
            connection.close()

def validate_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    # Replace this with your own token validation logic
    valid_token = "Xq7Zy9Bk2Lm3Hn5Jf8Uw1Pv6Rt0Oi4Ns9Qe7Cd8Mx2Gy6Tj5"

    if credentials.credentials != valid_token:
        raise HTTPException(status_code=401, detail="Invalid token")

    return credentials.credentials

def get_chat_history(from_id: int, chat_id: int):
    logger = logging.getLogger(__name__)
    logger.debug(f"Retrieving chat history for from_id: {from_id}, chat_id: {chat_id}")

    with get_db_connection() as mydb:
        # Create a cursor object
        mycursor = mydb.cursor()
        
        # Execute a SQL query
        sql_query = f'''
            SELECT CONCAT('Chat History:\n\n', 
            GROUP_CONCAT(DISTINCT content ORDER BY timestamp DESC SEPARATOR '\n'), 
            '\n') AS merged_content 
            FROM tb_message
            WHERE (from_id = {from_id} AND chat_id = {chat_id} AND chat_type = "CHAT")
            ORDER BY MAX(timestamp) DESC;

        '''
        mycursor.execute(sql_query)

        # Fetch the results
        results = mycursor.fetchall()

        # Close the cursor
        mycursor.close()

    return results

def get_user_kb(user_id: int):
    logger = logging.getLogger(__name__)
    logger.debug(f"Retrieving user knowledge base for user_id: {user_id}")

    with get_db_connection() as mydb:
        # Create a cursor object
        mycursor = mydb.cursor()
        
        # Execute a SQL query
        sql_query = f'''
            SELECT CONCAT(
            'User knowledge base:\n\n',
            GROUP_CONCAT(content SEPARATOR '\n'),
            '\n'
            ) AS merged_content
            FROM tb_information
            WHERE user_id = {user_id} AND title <> '' AND content <> '';
        '''
        mycursor.execute(sql_query)

        # Fetch the results
        results = mycursor.fetchall()

        # Close the cursor
        mycursor.close()

    return results

def get_user_summary(user_id: int):
    logger = logging.getLogger(__name__)
    logger.debug(f"Retrieving user summary for user_id: {user_id}")

    with get_db_connection() as mydb:
        # Create a cursor object
        mycursor = mydb.cursor()
        
        # Execute the SQL query to get user mark
        sql_query = f"SELECT mark FROM sys_user WHERE id = {user_id}"
        mycursor.execute(sql_query)

        # Fetch the results
        results = mycursor.fetchone()

        # Close the cursor
        mycursor.close()

    return results[0] if results else None

def get_conversation(from_id: int, chat_id: int):
    logger = logging.getLogger(__name__)
    logger.debug(f"Retrieving conversation between from_id: {from_id} and chat_id: {chat_id}")

    with get_db_connection() as mydb:
        # Create a cursor object
        mycursor = mydb.cursor()
        
        # Execute a SQL query
        sql_query = f'''
            SELECT
                CONCAT(
                    GROUP_CONCAT(
                        CASE
                            WHEN from_id = {from_id} THEN CONCAT('[', name, ']: ', content)
                            WHEN from_id = {chat_id} THEN CONCAT('[', name, ']: ', content)
                        END
                        ORDER BY timestamp ASC
                        SEPARATOR '\n'
                    )
                ) AS conversation
            FROM
                tb_message
            WHERE
                (from_id = {from_id} AND chat_id = {chat_id})
                OR (from_id = {chat_id} AND chat_id = {from_id});
        '''
        mycursor.execute(sql_query)

        # Fetch the results
        results = mycursor.fetchall()

        # Close the cursor
        mycursor.close()

    return results[0] if results else None

@app.get("/chat_history")
def read_chat_history(
    token: str = Depends(validate_token), 
    from_id: int = None, 
    chat_id: int = None
):
    if from_id is None or chat_id is None:
        raise HTTPException(status_code=400, detail="Both from_id and chat_id must be provided")

    chat_history = get_chat_history(from_id, chat_id)
    return {"chat_history": chat_history}

@app.get("/user_kb")
def read_user_kb(token: str = Depends(validate_token), user_id: int = None):
    if user_id is None:
        raise HTTPException(status_code=400, detail="user_id must be provided")

    user_kb = get_user_kb(user_id)
    return {"user_kb": user_kb}

@app.get("/user_summary")
def read_user_summary(
    token: str = Depends(validate_token), 
    user_id: int = None
):
    if user_id is None:
        raise HTTPException(status_code=400, detail="user_id must be provided")

    user_summary = get_user_summary(user_id)
    return {"user_summary": user_summary}

@app.get("/conversation")
def read_conversation(
    token: str = Depends(validate_token),
    from_id: int = None,
    chat_id: int = None
):
    if from_id is None or chat_id is None:
        raise HTTPException(status_code=400, detail="Both from_id and chat_id must be provided")

    conversation = get_conversation(from_id, chat_id)
    return {"conversation": conversation}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
